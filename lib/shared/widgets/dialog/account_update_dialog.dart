import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/injection_container.dart';

import '../../../features/routers/app_router.dart';
import '../../../features/routers/navigator_utils.dart';

class AccountUpdateDialog {
  AccountUpdateDialog();

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) => AccountUpdateCubit(),
          child: const _AccountUpdateDialogContent(),
        );
      },
    );
  }
}

class _AccountUpdateDialogContent extends StatelessWidget {
  const _AccountUpdateDialogContent();

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 362.gw,
          padding: EdgeInsets.all(24.gw),
          decoration: BoxDecoration(
            gradient: const RadialGradient(
              center: Alignment(0, -0.27),
              radius: 1.0,
              colors: [
                Color(0xFF4C3713),
                Color(0xFF181818),
                Color(0xFF0F0F0F),
              ],
              stops: [0.0, 0.35, 1.0],
            ),
            border: Border.all(
              width: 1,
              color: const Color(0xFF8C6C37),
            ),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCloseButton(context),
              _buildTitle(context),
              SizedBox(height: 40.gw),
              _buildForm(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 32.gw,
          height: 32.gw,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            border: Border.all(color: const Color(0xFFB4B3B3)),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Icon(
            Icons.close,
            color: Colors.white,
            size: 16.gw,
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return AneText(
      'add_account_information'.tr(),
      style: context.textTheme.primary.fs24.w600.copyWith(
        color: Colors.white,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildForm(BuildContext context) {
    return BlocBuilder<AccountUpdateCubit, AccountUpdateState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrencyField(context, state),
            SizedBox(height: 24.gw),
            _buildNicknameField(context, state),
            SizedBox(height: 40.gw),
            _buildSubmitButton(context, state),
          ],
        );
      },
    );
  }

  Widget _buildCurrencyField(BuildContext context, AccountUpdateState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AneText(
          '* ${'select_currency'.tr()}',
          style: context.textTheme.secondary.fs16.copyWith(
            color: const Color(0xFFB4B3B3),
          ),
        ),
        SizedBox(height: 16.gw),
        GestureDetector(
          onTap: () => context.read<AccountUpdateCubit>().showCurrencyPicker(context),
          child: Container(
            width: 314.gw,
            height: 48.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF212121)),
              borderRadius: BorderRadius.circular(12.gw),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AneText(
                  state.selectedCurrency?.name ?? 'select_currency'.tr(),
                  style: context.textTheme.secondary.copyWith(
                    color: state.selectedCurrency != null ? const Color(0xFFB4B3B3) : const Color(0xFF636363),
                  ),
                ),
                Container(
                  width: 30.gw,
                  height: 30.gw,
                  decoration: BoxDecoration(
                    color: const Color(0xFF6F5502),
                    borderRadius: BorderRadius.circular(12.gw),
                  ),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: const Color(0xFF9F9E9C),
                    size: 16.gw,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNicknameField(BuildContext context, AccountUpdateState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AneText(
          'set_nickname'.tr(),
          style: context.textTheme.secondary.fs16.copyWith(
            color: const Color(0xFFB4B3B3),
          ),
        ),
        SizedBox(height: 16.gw),
        IconTextfield(
          controller: state.nicknameController,
          hintText: 'enter_nickname_optional'.tr(),
          prefixIcon: Icon(
            Icons.person_outline,
            color: const Color(0xFF9F9E9C),
            size: 20.gw,
          ),
          onChanged: (value) => context.read<AccountUpdateCubit>().updateNickname(value),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BuildContext context, AccountUpdateState state) {
    return SizedBox(
      width: 314.gw,
      child: CommonButton(
        title: 'submit'.tr(),
        style: CommonButtonStyle.primary,
        onPressed: state.canSubmit ? () => context.read<AccountUpdateCubit>().submit(context) : null,
      ),
    );
  }
}

// Cubit for managing dialog state
class AccountUpdateCubit extends Cubit<AccountUpdateState> {
  AccountUpdateCubit() : super(AccountUpdateState());

  void updateNickname(String nickname) {
    emit(state.copyWith(nickname: nickname));
  }

  void selectCurrency(CurrencyConfig currency) {
    emit(state.copyWith(selectedCurrency: currency));
  }

  void showCurrencyPicker(BuildContext context) {
    final currencies = GlobalConfig().systemConfig.currencyList;

    showModalBottomSheet(
      context: context,
      backgroundColor: context.theme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.gw)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.gw),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AneText(
              'select_currency'.tr(),
              style: context.textTheme.primary.fs18.w600,
            ),
            SizedBox(height: 20.gw),
            ...currencies.map((currency) => ListTile(
                  title: AneText(
                    currency.name,
                    style: context.textTheme.primary.fs16,
                  ),
                  onTap: () {
                    selectCurrency(currency);
                    Navigator.pop(context);
                  },
                )),
          ],
        ),
      ),
    );
  }

  void submit(BuildContext context) {
    // TODO: Implement submit logic
    Navigator.of(context).pop();
    sl<NavigatorService>().push(AppRouter.userProfile);
  }

  @override
  Future<void> close() {
    state.nicknameController.dispose();
    return super.close();
  }
}

// State class for the dialog
class AccountUpdateState {
  final String nickname;
  final CurrencyConfig? selectedCurrency;
  final TextEditingController nicknameController;

  AccountUpdateState({
    this.nickname = '',
    this.selectedCurrency,
    TextEditingController? nicknameController,
  }) : nicknameController = nicknameController ?? TextEditingController();

  bool get canSubmit => selectedCurrency != null;

  AccountUpdateState copyWith({
    String? nickname,
    CurrencyConfig? selectedCurrency,
  }) {
    return AccountUpdateState(
      nickname: nickname ?? this.nickname,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      nicknameController: nicknameController,
    );
  }
}
